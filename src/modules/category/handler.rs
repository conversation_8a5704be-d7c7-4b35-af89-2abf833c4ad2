use crate::{
    constants::API_CATEGORIES_PATH,
    errors::Result,
    modules::category::{
        models::{
            CategoryPaginationRequest, CategoryWithTracking, CreateCategoryRequest,
            PaginatedCategoriesWithTracking, UpdateCategoryRequest,
        },
        service_trait::CategoryServiceTrait,
    },
    response::{ApiResponse, ApiResponseJson},
    routes::middleware::auth::AuthenticatedUser,
    utils::response_helpers::{response_constants::category, ResponseHelper},
};
use axum::{
    extract::{Path, Query, Request, State},
    Json,
};
use std::sync::Arc;
use uuid::Uuid;

#[utoipa::path(
    post,
    path = "/api/categories",
    tag = "Categories",
    request_body = crate::modules::category::models::CreateCategoryRequest,
    responses(
        (status = 201, description = "Category created successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Category already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Json(create_request): Json<CreateCategoryRequest>,
    request: Request,
) -> Result<impl axum::response::IntoResponse> {
    // Extract authenticated user from request extensions
    let user_info = request
        .extensions()
        .get::<AuthenticatedUser>()
        .ok_or_else(|| crate::errors::AppError::Unauthorized("User not authenticated".into()))?;

    let user_id = Uuid::parse_str(&user_info.user_id)
        .map_err(|_| crate::errors::AppError::Internal(anyhow::anyhow!("Invalid user ID format")))?;

    let category = category_service.create_category(create_request, &user_id).await?;

    Ok(ResponseHelper::entity_created(
        API_CATEGORIES_PATH,
        category::SUCCESS_CREATE,
        category::MSG_CREATED,
        category,
    ))
}

#[utoipa::path(
    get,
    path = "/api/categories/{id}",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    responses(
        (status = 200, description = "Category retrieved successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 404, description = "Category not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    let category = category_service.get_category_by_id(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&id.to_string()),
        category::SUCCESS_GET,
        category::MSG_RETRIEVED,
        category,
    ))
}

#[utoipa::path(
    get,
    path = "/api/categories",
    tag = "Categories",
    params(
        CategoryPaginationRequest
    ),
    responses(
        (status = 200, description = "Categories retrieved successfully", body = ApiResponse<PaginatedCategoriesWithTracking>),
        (status = 400, description = "Invalid pagination parameters")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_categories_with_pagination(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Query(pagination_request): Query<CategoryPaginationRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let categories = category_service
        .get_categories_with_pagination_and_tracking(pagination_request)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        None,
        category::SUCCESS_LIST,
        category::MSG_LIST_RETRIEVED,
        categories,
    ))
}

#[utoipa::path(
    get,
    path = "/api/categories/type/{category_type}",
    tag = "Categories",
    params(
        ("category_type" = String, Path, description = "Category type (e.g., 'laptops', 'phones')")
    ),
    responses(
        (status = 200, description = "Categories retrieved successfully", body = ApiResponse<Vec<CategoryWithTracking>>),
        (status = 400, description = "Invalid category type")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_categories_by_type(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(category_type): Path<String>,
) -> Result<impl axum::response::IntoResponse> {
    let categories = category_service
        .get_categories_by_type_with_tracking(&category_type)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&format!("type/{}", category_type)),
        category::SUCCESS_LIST,
        category::MSG_LIST_RETRIEVED,
        categories,
    ))
}

#[utoipa::path(
    put,
    path = "/api/categories/{id}",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    request_body = crate::modules::category::models::UpdateCategoryRequest,
    responses(
        (status = 200, description = "Category updated successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Category not found"),
        (status = 409, description = "Category name already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(update_request): Json<UpdateCategoryRequest>,
    request: Request,
) -> Result<impl axum::response::IntoResponse> {
    // Extract authenticated user from request extensions
    let user_info = request
        .extensions()
        .get::<AuthenticatedUser>()
        .ok_or_else(|| crate::errors::AppError::Unauthorized("User not authenticated".into()))?;

    let user_id = Uuid::parse_str(&user_info.user_id)
        .map_err(|_| crate::errors::AppError::Internal(anyhow::anyhow!("Invalid user ID format")))?;

    let category = category_service.update_category(&id, update_request, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&id.to_string()),
        category::SUCCESS_UPDATE,
        category::MSG_UPDATED,
        category,
    ))
}

#[utoipa::path(
    delete,
    path = "/api/categories/{id}",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    responses(
        (status = 200, description = "Category deleted successfully", body = ApiResponseJson),
        (status = 404, description = "Category not found"),
        (status = 409, description = "Category is being used and cannot be deleted")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    category_service.delete_category(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_CATEGORIES_PATH,
        &id.to_string(),
        category::SUCCESS_DELETE,
        category::MSG_DELETED,
    ))
}

#[utoipa::path(
    patch,
    path = "/api/categories/{id}/toggle-status",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    responses(
        (status = 200, description = "Category status toggled successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 404, description = "Category not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn toggle_category_status(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
    request: Request,
) -> Result<impl axum::response::IntoResponse> {
    // Extract authenticated user from request extensions
    let user_info = request
        .extensions()
        .get::<AuthenticatedUser>()
        .ok_or_else(|| crate::errors::AppError::Unauthorized("User not authenticated".into()))?;

    let user_id = Uuid::parse_str(&user_info.user_id)
        .map_err(|_| crate::errors::AppError::Internal(anyhow::anyhow!("Invalid user ID format")))?;

    let category = category_service.toggle_category_status(&id, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&format!("{}/toggle-status", id)),
        category::SUCCESS_UPDATE,
        "Category status toggled successfully",
        category,
    ))
}
