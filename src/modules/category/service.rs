use crate::database::Database;
use crate::errors::{AppError, Result};
use crate::modules::category::models::{
    Category, CategoryPaginationRequest, CategoryWithTracking, CreateCategoryRequest,
    PaginatedCategories, PaginatedCategoriesWithTracking, UpdateCategory, UpdateCategoryRequest,
};
use crate::modules::category::repository::{CreateCategoryParams, DynCategoryRepo};
use crate::modules::category::service_trait::{
    CategoryManagementTrait, CategoryQueryTrait, CategoryServiceTrait, CategoryValidationTrait,
};
use crate::utils::validation::helpers::validate_enhanced_and_execute;
use crate::utils::ErrorHelper;
use async_trait::async_trait;
use chrono::Utc;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Clone)]
pub struct CategoryService {
    repository: DynCategoryRepo,
}

impl CategoryService {
    pub fn new(repository: DynCategoryRepo) -> Self {
        Self { repository }
    }
}

// Implement CategoryQueryTrait
#[async_trait]
impl CategoryQueryTrait for CategoryService {
    async fn get_category_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking> {
        self.repository.get_by_id(id).await
    }

    async fn get_category_by_slug_and_type(&self, slug: &str, category_type: &str) -> Result<CategoryWithTracking> {
        self.repository.get_by_slug_and_type(slug, category_type).await
    }

    async fn get_all_categories(&self) -> Result<Vec<Category>> {
        self.repository.get_all().await
    }

    async fn get_all_categories_with_tracking(&self) -> Result<Vec<CategoryWithTracking>> {
        self.repository.get_all_with_tracking().await
    }

    async fn get_categories_by_type(&self, category_type: &str) -> Result<Vec<Category>> {
        self.repository.get_by_type(category_type).await
    }

    async fn get_categories_by_type_with_tracking(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>> {
        self.repository.get_by_type_with_tracking(category_type).await
    }

    async fn get_categories_with_pagination(
        &self,
        pagination: CategoryPaginationRequest,
    ) -> Result<PaginatedCategories> {
        self.repository.get_with_pagination(pagination).await
    }

    async fn get_categories_with_pagination_and_tracking(
        &self,
        pagination: CategoryPaginationRequest,
    ) -> Result<PaginatedCategoriesWithTracking> {
        self.repository.get_with_pagination_and_tracking(pagination).await
    }
}

// Implement CategoryManagementTrait
#[async_trait]
impl CategoryManagementTrait for CategoryService {
    async fn create_category(&self, request: CreateCategoryRequest, created_by: &Uuid) -> Result<CategoryWithTracking> {
        // Validate request using enhanced validation
        validate_enhanced_and_execute(&request, || async {
            // Check for duplicate name within the same category type
            self.validate_category_name_unique(&request.name, &request.category_type, None).await?;
            
            // Check for duplicate slug within the same category type
            self.validate_category_slug_unique(&request.slug, &request.category_type, None).await?;

            // Create category
            let params = CreateCategoryParams {
                name: request.name,
                slug: request.slug,
                description: request.description,
                category_type: request.category_type,
                is_active: true, // New categories are active by default
                created_by: Some(*created_by),
            };

            self.repository.create(params).await
        }).await
    }

    async fn update_category(&self, id: &Uuid, request: UpdateCategoryRequest, updated_by: &Uuid) -> Result<CategoryWithTracking> {
        // Validate request using enhanced validation
        validate_enhanced_and_execute(&request, || async {
            // Check if category exists
            let existing_category = self.repository.get_by_id(id).await?;

            // If name is being updated, check for duplicates within the same category type
            if let Some(ref new_name) = request.name {
                self.validate_category_name_unique(new_name, &existing_category.category_type, Some(id)).await?;
            }

            // Update category
            let update_data = UpdateCategory {
                name: request.name,
                description: request.description,
                is_active: request.is_active,
                updated_by: Some(*updated_by),
                updated_at: Utc::now(),
            };

            self.repository.update(id, update_data).await
        }).await
    }

    async fn delete_category(&self, id: &Uuid) -> Result<()> {
        // Check if category exists before deletion
        self.validate_category_exists(id).await?;
        
        // TODO: Check if category is being used by any items (laptops, etc.)
        // This should be implemented when we add the laptop module
        
        self.repository.delete(id).await
    }

    async fn toggle_category_status(&self, id: &Uuid, updated_by: &Uuid) -> Result<CategoryWithTracking> {
        // Get current category
        let current_category = self.repository.get_by_id(id).await?;
        
        // Toggle the status
        let update_data = UpdateCategory {
            name: None,
            description: None,
            is_active: Some(!current_category.is_active),
            updated_by: Some(*updated_by),
            updated_at: Utc::now(),
        };

        self.repository.update(id, update_data).await
    }
}

// Implement CategoryValidationTrait
#[async_trait]
impl CategoryValidationTrait for CategoryService {
    async fn validate_category_name_unique(&self, name: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        let exists = self.repository.exists_by_name_and_type(name, category_type, exclude_id).await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Category with name '{}' already exists in category type '{}'",
                name, category_type
            )));
        }
        Ok(())
    }

    async fn validate_category_slug_unique(&self, slug: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        let exists = self.repository.exists_by_slug_and_type(slug, category_type, exclude_id).await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Category with slug '{}' already exists in category type '{}'",
                slug, category_type
            )));
        }
        Ok(())
    }

    async fn validate_category_exists(&self, id: &Uuid) -> Result<()> {
        // This will return NotFound error if category doesn't exist
        self.repository.get_by_id(id).await?;
        Ok(())
    }
}
