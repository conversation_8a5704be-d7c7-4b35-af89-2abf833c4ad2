#![warn(clippy::redundant_clone)]

use platform_rust::{
    config::Config,
    database::Database,
    modules::email::{EmailEventBus, EmailService},
    routes::create_routes,
};

use std::net::SocketAddr;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;
use std::sync::Arc;

// ====== OpenAPI definition ======
#[derive(OpenApi)]
#[openapi(
    info(
        title = "Platform Rust API",
        description = "A comprehensive platform API built with Rust and Axum",
        version = "1.0.0",
        contact(
            name = "API Support",
            email = "<EMAIL>"
        ),
        license(
            name = "MIT",
            url = "https://opensource.org/licenses/MIT"
        )
    ),
    servers(
        (url = "http://localhost:8386", description = "Development server"),
        (url = "https://api.platform-rust.com", description = "Production server")
    ),
    paths(
        // Auth
        platform_rust::modules::auth::handler::register,
        platform_rust::modules::auth::handler::login,
        platform_rust::modules::auth::handler::refresh_token,
        platform_rust::modules::auth::handler::oauth_login,
        platform_rust::modules::auth::handler::oauth_callback,
        // Users
        platform_rust::modules::user::handler::create_user,
        platform_rust::modules::user::handler::get_user,
        platform_rust::modules::user::handler::get_users_with_pagination,
        platform_rust::modules::user::handler::update_user,
        platform_rust::modules::user::handler::delete_user,
        platform_rust::modules::user::handler::get_user_roles,
        platform_rust::modules::user::handler::update_user_roles,
        // Permissions
        platform_rust::modules::permission::handler::create_permission,
        platform_rust::modules::permission::handler::get_permission,
        platform_rust::modules::permission::handler::get_permissions,
        platform_rust::modules::permission::handler::update_permission,
        platform_rust::modules::permission::handler::delete_permission,
        // Roles
        platform_rust::modules::role::handler::create_role,
        platform_rust::modules::role::handler::get_role,
        platform_rust::modules::role::handler::get_roles,
        platform_rust::modules::role::handler::update_role,
        platform_rust::modules::role::handler::delete_role,
        platform_rust::modules::role::handler::get_role_permissions,
        platform_rust::modules::role::handler::update_role_permissions,
        platform_rust::modules::role::handler::update_permission_matrix,
        // Categories
        platform_rust::modules::category::handler::create_category,
        platform_rust::modules::category::handler::get_category,
        platform_rust::modules::category::handler::get_categories,
        platform_rust::modules::category::handler::get_categories_with_tracking,
        platform_rust::modules::category::handler::get_categories_by_type,
        platform_rust::modules::category::handler::get_categories_by_type_with_tracking,
        platform_rust::modules::category::handler::get_categories_with_pagination,
        platform_rust::modules::category::handler::get_categories_with_pagination_and_tracking,
        platform_rust::modules::category::handler::update_category,
        platform_rust::modules::category::handler::delete_category,
        platform_rust::modules::category::handler::toggle_category_status,
    ),
    components(
        schemas(
            // Common
            platform_rust::response::ApiResponseJson,
            platform_rust::response::ApiResponse<Vec<platform_rust::modules::permission::models::Permission>>,
            platform_rust::response::ApiResponse<platform_rust::modules::user::models::UserWithRoles>,
            platform_rust::response::ApiResponse<platform_rust::modules::user::models::PaginatedUsersWithRoles>,
            platform_rust::response::ApiResponse<platform_rust::modules::user::models::UserRolesResponse>,
            platform_rust::response::ApiResponse<platform_rust::modules::permission::models::Permission>,
            platform_rust::response::ApiResponse<platform_rust::modules::permission::models::PaginatedPermissions>,
            platform_rust::response::ApiResponse<platform_rust::modules::role::models::Role>,
            platform_rust::response::ApiResponse<platform_rust::modules::role::models::PaginatedRoles>,
            platform_rust::response::ApiResponse<platform_rust::modules::role::models::PermissionMatrixUpdateResponse>,
            platform_rust::response::ApiResponse<platform_rust::modules::auth::models::LoginResponse>,
            platform_rust::response::ApiResponse<platform_rust::modules::category::models::CategoryWithTracking>,
            platform_rust::response::ApiResponse<Vec<platform_rust::modules::category::models::Category>>,
            platform_rust::response::ApiResponse<Vec<platform_rust::modules::category::models::CategoryWithTracking>>,
            platform_rust::response::ApiResponse<platform_rust::modules::category::models::PaginatedCategories>,
            platform_rust::response::ApiResponse<platform_rust::modules::category::models::PaginatedCategoriesWithTracking>,
            // Auth
            platform_rust::modules::auth::models::RegisterRequest,
            platform_rust::modules::auth::models::LoginRequest,
            platform_rust::modules::auth::models::LoginResponse,
            platform_rust::modules::auth::models::RefreshTokenRequest,
            // Users
            platform_rust::modules::user::models::CreateUserRequest,
            platform_rust::modules::user::models::UpdateUserRequest,
            platform_rust::modules::user::models::User,
            platform_rust::modules::user::models::UserWithRoles,
            platform_rust::modules::user::models::UserPaginationRequest,
            platform_rust::modules::user::models::PaginatedUsersWithRoles,
            platform_rust::modules::user::models::UserPagination,
            platform_rust::modules::user::models::UserRolesResponse,
            platform_rust::modules::user::models::UpdateUserRolesRequest,
            // Permissions
            platform_rust::modules::permission::models::CreatePermissionRequest,
            platform_rust::modules::permission::models::UpdatePermissionRequest,
            platform_rust::modules::permission::models::Permission,
            platform_rust::modules::permission::models::PermissionPaginationRequest,
            platform_rust::modules::permission::models::PaginatedPermissions,
            // Roles
            platform_rust::modules::role::models::CreateRoleRequest,
            platform_rust::modules::role::models::UpdateRoleRequest,
            platform_rust::modules::role::models::Role,
            platform_rust::modules::role::models::RolePaginationRequest,
            platform_rust::modules::role::models::PaginatedRoles,
            platform_rust::modules::role::models::ReplaceRolePermissionsRequest,
            platform_rust::modules::role::models::PermissionMatrixUpdateRequest,
            platform_rust::modules::role::models::PermissionMatrixUpdateResponse,
            platform_rust::modules::role::models::PermissionMatrixChange,
            // Categories
            platform_rust::modules::category::models::CreateCategoryRequest,
            platform_rust::modules::category::models::UpdateCategoryRequest,
            platform_rust::modules::category::models::Category,
            platform_rust::modules::category::models::CategoryWithTracking,
            platform_rust::modules::category::models::CategoryPaginationRequest,
            platform_rust::modules::category::models::PaginatedCategories,
            platform_rust::modules::category::models::PaginatedCategoriesWithTracking,
        )
    ),
    tags(
        (name = "Auth", description = "Authentication and authorization endpoints"),
        (name = "Users", description = "User management and profile operations"),
        (name = "Permissions", description = "Permission and role-based access control"),
        (name = "Roles", description = "Role management for RBAC system"),
        (name = "Categories", description = "Category management for organizing content")
    ),
    modifiers(&SecurityAddon)
)]
struct ApiDoc;

// Security configuration for OpenAPI
struct SecurityAddon;

impl utoipa::Modify for SecurityAddon {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        use utoipa::openapi::security::{HttpAuthScheme, HttpBuilder, SecurityScheme};

        if let Some(components) = openapi.components.as_mut() {
            components.add_security_scheme(
                "bearer_auth",
                SecurityScheme::Http(
                    HttpBuilder::new()
                        .scheme(HttpAuthScheme::Bearer)
                        .bearer_format("JWT")
                        .build(),
                ),
            )
        }
    }
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // --- Enhanced Tracing Setup ---
    
    let env_filter = tracing_subscriber::EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| "platform_rust=info,tower_http=info".into());

    // Console layer with structured output
    let console_layer = tracing_subscriber::fmt::layer()
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .with_ansi(true);

    // Initialize based on LOG_JSON environment variable
    if std::env::var("LOG_JSON").unwrap_or_default() == "true" {
        // JSON output for production
        tracing_subscriber::registry()
            .with(env_filter)
            .with(
                tracing_subscriber::fmt::layer()
                    .json()
                    .with_current_span(false)
                    .with_span_list(true),
            )
            .init();
    } else {
        // Console output for development
        tracing_subscriber::registry()
            .with(env_filter)
            .with(console_layer)
            .init();
    }

    // --- Config & DB ---
    let config = Config::from_env()?;
    let db_config = config
        .database
        .to_database_config(config.database_url.clone());
    let database = Database::with_config(db_config).await?;
    database.migrate().await?;

    // Create service container early
    let mut container = platform_rust::container::service_container::ServiceContainer::new(database.clone(), config.clone());
    container.init_redis_service(&config).await?;

    // --- Initialize default data if needed ---
    if let Err(e) = platform_rust::init::initialize_database(database.clone()).await {
        // Log warning but don't fail startup - data might already exist
        tracing::warn!("Database initialization: {}", e);
    } else {
        tracing::info!("✅ Database initialization completed");
    }

    // --- Initialize Email Service ---
    let app_url = format!("http://{}", config.server_address());
    let app_name = "Platform Rust".to_string();

    // Create email event bus with Redis
    let email_event_bus = Arc::new(EmailEventBus::new(container.redis_service().unwrap()));

    let mut email_service =
        EmailService::new(email_event_bus.clone(), config.email.clone(), app_url, app_name);

    // Initialize email service
    if let Err(e) = email_service.initialize().await {
        tracing::warn!("Email service initialization failed: {}", e);
    } else {
        tracing::info!("✅ Email service initialized");
    }

    // Start email service as background task
    tokio::spawn(async move {
        if let Err(e) = email_service.start_listening().await {
            tracing::error!("Email service failed: {}", e);
        }
    });

    // --- Router ---
    let mut app = create_routes(database, config.clone(), email_event_bus.clone());

    // === Attach API Documentation ===
    let openapi = ApiDoc::openapi();

    // Swagger UI
    app = app.merge(
        SwaggerUi::new("/swagger-ui")
            .url("/api-docs/openapi.json", openapi.clone())
            .config(utoipa_swagger_ui::Config::new(["/api-docs/openapi.json"])),
    );

    // --- Start server ---
    let listener = tokio::net::TcpListener::bind(&config.server_address()).await?;
    tracing::info!("🚀 Server running on {}", config.server_address());
    tracing::info!("📚 API Documentation:");
    tracing::info!(
        "  - Swagger UI: http://{}/swagger-ui",
        config.server_address()
    );
    tracing::info!(
        "  - OpenAPI:    http://{}/api-docs/openapi.json",
        config.server_address()
    );
    axum::serve(
        listener,
        app.into_make_service_with_connect_info::<SocketAddr>(),
    )
    .await?;
    Ok(())
}
